#!/bin/bash

# Define the directories and file paths
directories=(
  "../../services/task-manager/src/main/resources"
  "../../services/integration-data-sync/src/main/resources"
  "../../services/findings-api/src/main/resources"
  "../../services/integrations-manager/src/main/resources"
  "../../services/findings-persistence/src/main/resources"
)

# Define the content for each application-local-docker.yml file
declare -a contents
contents[0]="
logging:
  level:
    ROOT: INFO
spring:
  application:
    name: task-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

  #  Postgres db config
  r2dbc:
    url: r2dbc:postgresql://docker-postgres-1/integrations
    username: admin
    password: admin
  data:
    r2dbc:
      repositories:
        enabled: true

  # Cosmos no SQL Database Config
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: <db_key>
        database: integrations

server:
  address: 0.0.0.0
  port: 8081

launch-darkly:
  sdk-key: <ld_sdk_key>

task-manager:
  task-purge-config:
    slow-task-purge-time: 300000
    fast-task-purge-time: 60000
  node-config:
    fast-node: true

  kafka-consumer-config:
    bootstrapServers: docker-kafka-1:9092
    isConnectionString: false
    topic: task-status-update-queue
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  kafka-light-task-producer-config:
    bootstrapServers: docker-kafka-1:9092
    topic: light-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false

  kafka-heavy-task-producer-config:
    bootstrapServers: docker-kafka-1:9092
    topic: heavy-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false
"

contents[1]="
logging:
  level:
    ROOT: INFO

spring:
  application:
    name: integration-data-sync
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
vault:
  uri: \"http://127.0.0.1:8200\"
  token: \"myroot\"
  namespace: admin

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2
server:
  address: 0.0.0.0
  port: 8090
integration-data-sync:
  task-consumer-mode: HEAVY
  integrations-config:
    wiz:
      projects-api-max-page-size: 500
      findings-apis-max-page-size: 50
      sync-data-window-length-fallback: 1d
      min-time-between-report-creation-calls: 1s

  kafka-task-status-producer-config:
    bootstrapServers: docker-kafka-1:9092
    isConnectionString: false
    topic: task-status-update-queue

  kafka-findings-producer-config:
    bootstrapServers: docker-kafka-1:9092
    isConnectionString: false
    topic: vulnerabilities-queue

  kafka-light-task-consumer-config:
    topic: light-task-queue
    isConnectionString: false
    bootstrapServers: docker-kafka-1:9092
    groupId: integration-data-sync
    autoOffsetReset: earliest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10

  kafka-heavy-task-consumer-config:
    topic: heavy-task-queue
    isConnectionString: false
    bootstrapServers: docker-kafka-1:9092
    groupId: integration-data-sync
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10
"

contents[2]="
logging:
  level:
    ROOT: INFO
spring:
  application:
    name: findings-api
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/integrations
    username: admin
    password: admin
  springdoc:
    swagger-ui:
      path: /swagger-ui.html
server:
  address: 0.0.0.0
  port: 8084
census:
  auth:
    enabled: true
grpc:
  channel:
    host: dev-census-grpc.console.ilabs.io
    port: 443
    enableMtls: false
    caCert: ""
    mtlsKey: ""
    mtlsCert: ""
  server:
    port: 9090


"

contents[3]="
logging:
  level:
    ROOT: INFO
spring:
  ssl:
    disable-certificate-verification: true

  application:
    name: integrations-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: <db_key>
        database: integrations

  r2dbc:
    url: r2dbc:postgresql://docker-postgres-1/integrations
    username: admin
    password: admin

launch-darkly:
  sdk-key: <ld_sdk_key>

census:
  auth:
    enabled: true

grpc:
  channel:
    host: dev-census-grpc.console.ilabs.io
    port: 443
    enableMtls: false
    caCert: ""
    mtlsKey: ""
    mtlsCert: ""
  server:
    port: 9091

server:
  address: 0.0.0.0
  port: 80
vault:
  uri: \"http://127.0.0.1:8200\"
  token: \"myroot\"
  namespace: admin

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

integrations-manager:
  integrations-config:
    wiz:
      name: Wiz
      description: Ingest cloud security issues and vulnerabilities from Wiz
      api-host: wiz.io
    armis:
      name: Armis
      description: Import Armis IoT/OT inventory and asset info to Illumio
    checkpoint:
      name: Checkpoint
      description: Ingest Checkpoint firewall logs for Insights
  kafka-light-task-producer-config:
    bootstrapServers: docker-kafka-1
    topic: light-task-queue
    isConnectionString: false

  kafka-heavy-task-producer-config:
    bootstrapServers: docker-kafka-1
    topic: heavy-task-queue
    isConnectionString: false
"

contents[4]="
logging:
  level:
    ROOT: INFO
server:
  address: 0.0.0.0
  port: 8080
spring:
  application:
    name: findings-persistence
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

  r2dbc:
    url: r2dbc:postgresql://docker-postgres-1/integrations
    username: admin
    password: admin

findings-persistence:
  kafka-consumer-config:
    bootstrapServers: docker-kafka-1
    isConnectionString: false
    topic: vulnerabilities-queue
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
"

# Check if each directory exists and create the application-local-docker.yml file if it doesn't exist
for ((i=0; i<${#directories[@]}; i++)); do
  directory=${directories[$i]}
  service_name=$(basename $(dirname $(dirname "$directory")))

  case $service_name in
    task-manager) service_index=0 ;;
    integration-data-sync) service_index=1 ;;
    findings-api) service_index=2 ;;
    integrations-manager) service_index=3 ;;
    findings-persistence) service_index=4 ;;
  esac


  if [ ! -d "$directory" ]; then
    echo "Error: Directory '$directory' does not exist."
    exit 1
  fi


  file_path="$directory/application-local-docker-jib.yml"


  echo "${contents[$i]}" >| "$file_path"
  echo "File created/overwritten successfully: $file_path"
done

echo "All configuration files have been generated or overwritten."
