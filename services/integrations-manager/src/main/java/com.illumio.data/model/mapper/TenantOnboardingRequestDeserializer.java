package com.illumio.data.model.mapper;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.illumio.data.model.*;

import java.io.IOException;

public class TenantOnboardingRequestDeserializer extends StdDeserializer<TenantOnboardingRequest> {

    public TenantOnboardingRequestDeserializer() {
        super(TenantOnboardingRequest.class);
    }

    @Override
    public TenantOnboardingRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        final ObjectMapper mapper = (ObjectMapper) p.getCodec();
        final JsonNode root = mapper.readTree(p);

        final Integration integration = Integration.valueOf(root.get("integration").asText().toUpperCase());
        final JsonNode metadataNode = root.get("tenantMetadata");
        final TenantMetadata tenantMetadata = mapper.treeToValue(metadataNode, TenantMetadata.getTenantMetadataClass(integration));

        final JsonNode configurationsNode = root.get("configurations");
        final TenantConfigurations configurations = mapper.treeToValue(configurationsNode, TenantConfigurations.getTenantConfigurationsClass(integration));

        return TenantOnboardingRequest.builder()
                                      .integration(integration)
                                      .tenantId(root.get("tenantId").asText())
                                      .credentials(mapper.treeToValue(root.get("credentials"), Credentials.class))
                                      .configurations(configurations)
                                      .tenantMetadata(tenantMetadata)
                                      .build();
    }
}