package com.illumio.data.controllers;

import com.illumio.data.AuthValidationService;
import com.illumio.data.components.TenantOnboardingService;
import com.illumio.data.components.TenantIntegrationStatusService;
import com.illumio.data.components.scripts.InjectTenantId;
import com.illumio.data.model.*;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.IntegrationManagerConstants.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(TENANT_INTEGRATIONS_BASE_API_PATH)
public class TenantIntegrationsController {

    private final TenantOnboardingService tenantOnboardingService;
    private final TenantIntegrationStatusService tenantIntegrationStatusService;
    private final AuthValidationService authValidationService;
    private final InjectTenantId injectTenantId;

    @PostMapping(ONBOARDING_API_PATH)
    public Mono<ResponseEntity<?>>  onboardTenant(@Valid @RequestHeader(X_CS_ID) String xcsId, final @RequestBody TenantOnboardingRequest request, ServerWebExchange exchange) {
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error -> log.error("Authorization failed for onboarding", error))
                                    .then(tenantOnboardingService.onboardTenant(request, true))
                                    .then(Mono.fromCallable(() -> ResponseEntity.ok().build()));
    }


    @PostMapping(TENANT_STATUS_API_PATH)
    public Mono<ResponseEntity<?>> fetchTenantIntegrationStatus(@Valid @RequestHeader(X_CS_ID) String xcsId, final @Valid @RequestBody TenantIntegrationStatusRequest request, ServerWebExchange exchange) {
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error -> log.error("Authorization failed for status check", error))
                                    .then(tenantIntegrationStatusService.fetchTenantIntegrationStatus(request))
                                    .map(ResponseEntity::ok);
    }

    @GetMapping(TENANT_ELIGIBLE_INTEGRATIONS_API_PATH)
    public Mono<IntegrationResponse> getEligibleIntegrations(@Valid @RequestHeader(X_CS_ID) String xcsId,
                                                             @PathVariable String tenantId,
                                                             ServerWebExchange exchange) {
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error -> log.error("Authorization failed for eligible integrations", error))
                                    .thenMany(tenantIntegrationStatusService.getEligibleIntegrations(tenantId))
                                    .collectList()
                                    .map(integrationResponseItems -> IntegrationResponse.builder().data(integrationResponseItems).build());
    }

    @GetMapping(LOG_EXPORTER_CONFIG_API_PATH)
    public Mono<ResponseEntity<ExporterConfigResponse>> getExporterConfig(@Valid @RequestHeader(X_CS_ID) String xcsId,
                                                                @PathVariable Integration integration,
                                                                @PathVariable String tenantId,
                                                                ServerWebExchange exchange) {
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error -> log.error("Authorization failed for exporter config", error))
                                    .then(injectTenantId.updateOriginValOnly("checkpoint_config.sh", tenantId))
                                    .then(tenantIntegrationStatusService.getExporterConfig(tenantId, integration))
                                    .map(ResponseEntity::ok);

    }

    @PatchMapping(ONBOARDING_API_PATH)
    public Mono<ResponseEntity<?>> patchOnboardedTenantData(@Valid @RequestHeader(X_CS_ID) String xcsId, @RequestBody TenantOnboardingRequest request, ServerWebExchange exchange ){
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error -> log.error("Authorization failed for onboarding", error))
                                    .then(tenantOnboardingService.onboardTenant(request, false))
                                    .then(Mono.fromCallable(() -> ResponseEntity.ok().build()));
    }
}