package com.illumio.data.components.scripts;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.*;
import java.nio.file.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

@Slf4j
@Component
@RequiredArgsConstructor
public class InjectTenantId {


    public Mono<Void> updateOriginValOnly(String scriptPath, String newTenantId) {
        System.out.println("Updating origin val only with tenant ID: " + newTenantId);
        return Mono.<Void>fromCallable(() -> {
                       try {
                           String originalContent = Files.readString(Paths.get(scriptPath));
                           Pattern pattern = Pattern.compile("^(ORIGIN_VAL=)\"([^\"]*)\"", Pattern.MULTILINE);
                           Matcher matcher = pattern.matcher(originalContent);

                           String updatedContent;
                           if (matcher.find()) {
                               updatedContent = matcher.replaceFirst("$1\"" + newTenantId + "\"");
                           } else {
                               throw new RuntimeException("ORIGIN_VAL not found in script: " + scriptPath);
                           }

                           Files.writeString(Paths.get(scriptPath), updatedContent);
                           log.info("Successfully updated ORIGIN_VAL in {} with tenant ID: {}", scriptPath, newTenantId);
                           return null;
                       } catch (IOException e) {
                           throw new RuntimeException("Failed to update script file: " + scriptPath, e);
                       }
                   })
                   .subscribeOn(Schedulers.boundedElastic());
    }
}