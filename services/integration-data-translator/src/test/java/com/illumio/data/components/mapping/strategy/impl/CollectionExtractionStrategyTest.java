package com.illumio.data.components.mapping.strategy.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.JsonNodeNavigator;
import com.illumio.data.components.mapping.JsonPathResolver;
import com.illumio.data.components.mapping.JsonValueConverter;
import com.illumio.data.components.mapping.TestResourceLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CollectionExtractionStrategyTest {

    @Mock
    private JsonPathResolver pathResolver;

    @Mock
    private JsonNodeNavigator navigator;

    @Mock
    private JsonValueConverter converter;

    @InjectMocks
    private CollectionExtractionStrategy strategy;

    private JsonNode testJsonNode;

    @BeforeEach
    void setUp() throws IOException {
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");
    }

    @Test
    void supports_shouldReturnTrue_whenPathContainsCollectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void supports_shouldReturnFalse_whenPathDoesNotContainCollectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnFalse_whenPathContainsOnlyObjectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].vulnerableAsset";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnTrue_whenPathContainsBothMarkersButCollectMarkerPresent() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection][@object].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void extract_shouldReturnListOfValues_whenArrayPathExists() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection].name";
        String cleanPath = "data.vulnerabilityFindings.nodes.name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 0;

        JsonNode arrayNode = TestResourceLoader.createJsonNode("""
            ["CVE-2022-1304", "CVE-2021-3445", "CVE-2022-32189"]
            """);


        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(arrayNode));
        
        // Mock converter for each array element
        when(converter.convertJsonNodeToValue(any(JsonNode.class)))
                .thenReturn(Mono.just("CVE-2022-1304"))
                .thenReturn(Mono.just("CVE-2021-3445"))
                .thenReturn(Mono.just("CVE-2022-32189"));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<String> list = (List<String>) value;
                    assertThat(list).hasSize(3);
                    assertThat(list).containsExactly("CVE-2022-1304", "CVE-2021-3445", "CVE-2022-32189");
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnCEFSemanticCollection_whenCEFArrayPathProvided() throws IOException {
        // Given
        String jsonPath = "events[@collection].severity";
        String cleanPath = "events.severity";
        String resolvedPath = "events.severity";
        int arrayIndex = -1; // No array index for CEF semantic structure

        JsonNode cefEventsArray = TestResourceLoader.createJsonNode("""
            ["High", "Medium", "Low", "Critical"]
            """);


        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(any(JsonNode.class), anyString())).thenReturn(Mono.just(cefEventsArray));
        
        // Mock converter for each severity level
        when(converter.convertJsonNodeToValue(any(JsonNode.class)))
                .thenReturn(Mono.just("High"))
                .thenReturn(Mono.just("Medium"))
                .thenReturn(Mono.just("Low"))
                .thenReturn(Mono.just("Critical"));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<String> severityList = (List<String>) value;
                    assertThat(severityList).hasSize(4);
                    assertThat(severityList).containsExactly("High", "Medium", "Low", "Critical");
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmptyList_whenArrayPathDoesNotExist() {
        // Given
        String jsonPath = "data.nonExistent[@collection].field";
        String cleanPath = "data.nonExistent.field";
        String resolvedPath = "data.nonExistent[0].field";
        int arrayIndex = 0;

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<?> list = (List<?>) value;
                    assertThat(list).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmptyList_whenNodeIsNotArray() throws IOException {
        // Given
        String jsonPath = "data.singleValue[@collection]";
        String cleanPath = "data.singleValue";
        String resolvedPath = "data.singleValue[0]";
        int arrayIndex = 0;

        JsonNode nonArrayNode = TestResourceLoader.createJsonNode("\"single-value\"");

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(nonArrayNode));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<?> list = (List<?>) value;
                    assertThat(list).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmptyList_whenArrayIsEmpty() throws IOException {
        // Given
        String jsonPath = "data.emptyArray[@collection].field";
        String cleanPath = "data.emptyArray.field";
        String resolvedPath = "data.emptyArray[0].field";
        int arrayIndex = 0;

        JsonNode emptyArrayNode = TestResourceLoader.createJsonNode("[]");

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(emptyArrayNode));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<?> list = (List<?>) value;
                    assertThat(list).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleMixedTypeArray_whenArrayContainsDifferentTypes() throws IOException {
        // Given
        String jsonPath = "data.mixedArray[@collection]";
        String cleanPath = "data.mixedArray";
        String resolvedPath = "data.mixedArray[0]";
        int arrayIndex = 0;

        JsonNode mixedArrayNode = TestResourceLoader.createJsonNode("""
            ["string", 42, true, 3.14, null]
            """);

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(mixedArrayNode));
        
        // Mock converter for each element
        when(converter.convertJsonNodeToValue(any(JsonNode.class)))
                .thenReturn(Mono.just("string"))
                .thenReturn(Mono.just(42))
                .thenReturn(Mono.just(true))
                .thenReturn(Mono.just(3.14))
                .thenReturn(Mono.empty()); // null value returns empty

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<Object> list = (List<Object>) value;
                    assertThat(list).hasSize(4); // null value is filtered out
                    assertThat(list.get(0)).isEqualTo("string");
                    assertThat(list.get(1)).isEqualTo(42);
                    assertThat(list.get(2)).isEqualTo(true);
                    assertThat(list.get(3)).isEqualTo(3.14);
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmptyList_whenNodeIsNull() throws IOException {
        // Given
        String jsonPath = "data.nullArray[@collection].field";
        String cleanPath = "data.nullArray.field";
        String resolvedPath = "data.nullArray[0].field";
        int arrayIndex = 0;

        JsonNode nullNode = TestResourceLoader.createNullJsonNode();

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(nullNode));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<?> list = (List<?>) value;
                    assertThat(list).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldPropagateError_whenNavigatorThrowsException() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collection].name";
        String cleanPath = "data.vulnerabilityFindings.nodes.name";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 0;

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(any(JsonNode.class), anyString()))
                .thenReturn(Mono.error(new RuntimeException("Navigation failed")));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }

    @Test
    void extract_shouldHandleConverterErrors_whenSomeElementsFailConversion() throws IOException {
        // Given
        String jsonPath = "data.problematicArray[@collection]";
        String cleanPath = "data.problematicArray";
        String resolvedPath = "data.problematicArray[0]";
        int arrayIndex = 0;

        JsonNode arrayNode = TestResourceLoader.createJsonNode("""
            ["valid1", "valid2", "problematic"]
            """);

        when(pathResolver.removeMarker(jsonPath, "[@collection]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(arrayNode));
        
        // Mock converter - first two succeed, third fails
        when(converter.convertJsonNodeToValue(any(JsonNode.class)))
                .thenReturn(Mono.just("valid1"))
                .thenReturn(Mono.just("valid2"))
                .thenReturn(Mono.error(new RuntimeException("Conversion failed")));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }
}
