package com.illumio.data.components.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {
    public static final String COLLECT_MARKER = "[@collection]";
    public static final String OBJECT_MARKER = "[@object]";

    public static final String JSON_FILE_EXTENSION = ".json";
    public static final String DATA_TYPE = "data-type";

    public static final String PATH_SEPARATOR_REGEX = "\\.";
    public static final String ARRAY_OPEN_BRACKET = "[";
    public static final String ARRAY_CLOSE_BRACKET = "]";

    public static final String ARRAY_WILDCARD = "[*]";
    public static final String ARRAY_WILDCARD_REGEX = "\\[\\*\\]";
    public static final String EMPTY_STRING = "";

    public static final String NAME = "name";
    public static final String DESCRIPTION = "description";
    public static final String FIELD_MAPPINGS = "fieldMappings";
}
