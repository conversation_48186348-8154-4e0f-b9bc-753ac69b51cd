package com.illumio.data.components;

import com.illumio.data.components.mapper.TaskStatusMapper;
import com.illumio.data.entities.RetryMessageEntity;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.model.TenantOnboarding;
import com.illumio.data.model.Integration;
import com.illumio.data.model.SubTaskStatus;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import com.illumio.data.repositories.RetryMessagesRepository;
import com.illumio.data.repositories.SubTaskStatusRepository;
import com.illumio.data.repositories.TaskStatusRepository;
import com.illumio.data.util.DateTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.illumio.data.util.DateTimeUtil.toOffsetDateTime;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskDbOperationsTest {
    @Mock
    private TaskStatusRepository taskStatusRepository;
    @Mock
    private RetryMessagesRepository retryMessagesRepository;
    @Mock
    private SubTaskStatusRepository subTaskStatusRepository;
    private TaskDbService taskDbService;

    private SubTaskStatus taskStatus;
    private TaskStatusEntity taskStatusEntity;

    @BeforeEach
    void setUp() {
        taskDbService = new TaskDbService(taskStatusRepository, subTaskStatusRepository, retryMessagesRepository);

        taskStatus = SubTaskStatus.builder()
                                  .taskId("1")
                                  .tenantId("test-tenant")
                                  .integration(Integration.WIZ)
                                  .statusType(TaskStatusType.SUCCESS)
                                  .taskType(TaskType.DELTA)
                                  .statusUpdateTime(DateTimeUtil.generateCurrentTimestamp())
                                  .statusMessage("")
                                  .build();
        taskStatusEntity = TaskStatusMapper.mapToTaskStatusEntity(taskStatus);
    }

    @Test
    void findLatestTask_shouldReturnTaskWhenExists() {
        TenantOnboarding onboarding = TenantOnboarding.builder()
                .tenantId("test-tenant")
                .integration(Integration.WIZ)
                .build();

        TaskStatusEntity activeTask = TaskStatusEntity.builder()
                                                      .taskId("1")
                                                      .status(TaskStatusType.STARTED)
                                                      .taskType(TaskType.DELTA)
                                                      .build();

        when(taskStatusRepository.findLatestByTenantIdAndIntegration(
                anyString(), any())).thenReturn(Mono.just(activeTask));

        StepVerifier.create(taskDbService.findLatestTask(onboarding))
                    .assertNext(result -> {
                        assertEquals("1", result.getTaskId());
                        assertEquals(TaskStatusType.STARTED, result.getStatusType());
                        assertEquals(TaskType.DELTA, result.getTaskType());
                    })
                    .verifyComplete();
    }

    @Test
    void determineTaskType_shouldReturnDeltaForSuccessfulTask() {
        TenantOnboarding onboarding = TenantOnboarding.builder()
                .tenantId("test-tenant")
                .integration(Integration.WIZ)
                .build();

        TaskStatusEntity completedTask = TaskStatusEntity.builder()
                                                         .taskId("2")
                                                         .status(TaskStatusType.SUCCESS)
                                                         .taskType(TaskType.DELTA)
                                                         .build();

        when(taskStatusRepository.findLatestByTenantIdAndIntegration(
                anyString(), any())).thenReturn(Mono.just(completedTask));

        StepVerifier.create(taskDbService.determineTaskType(onboarding))
                    .expectNext(TaskType.DELTA)
                    .verifyComplete();
    }

    @Test
    void determineTaskType_shouldReturnGreenfieldForFailedGreenfieldTask() {
        TenantOnboarding onboarding = TenantOnboarding.builder()
                .tenantId("test-tenant")
                .integration(Integration.WIZ)
                .build();

        TaskStatusEntity failedTask = TaskStatusEntity.builder()
                                                      .taskId("3")
                                                      .status(TaskStatusType.FAIL)
                                                      .taskType(TaskType.GREENFIELD)
                                                      .build();

        when(taskStatusRepository.findLatestByTenantIdAndIntegration(
                anyString(), any())).thenReturn(Mono.just(failedTask));

        StepVerifier.create(taskDbService.determineTaskType(onboarding))
                    .expectNext(TaskType.GREENFIELD)
                    .verifyComplete();
    }

    @Test
    void findLastSuccessfulTask_shouldReturnTaskWhenExists() {
        TenantOnboarding onboarding = TenantOnboarding.builder()
                .tenantId("test-tenant")
                .integration(Integration.WIZ)
                .build();

        TaskStatusEntity successfulTask = TaskStatusEntity.builder()
                                                          .taskId("4")
                                                          .status(TaskStatusType.SUCCESS)
                                                          .taskType(TaskType.DELTA)
                                                          .build();

        when(taskStatusRepository.findLastSuccessfulTask(anyString(), any()))
                .thenReturn(Mono.just(successfulTask));

        StepVerifier.create(taskDbService.findLastSuccessfulTask(onboarding))
                    .expectNext(successfulTask)
                    .verifyComplete();
    }

    @Test
    void findLastSuccessfulTask_shouldReturnEmptyWhenNoTaskExists() {
        TenantOnboarding onboarding = TenantOnboarding.builder()
                .tenantId("test-tenant")
                .integration(Integration.WIZ)
                .build();

        when(taskStatusRepository.findLastSuccessfulTask(anyString(), any()))
                .thenReturn(Mono.empty());

        StepVerifier.create(taskDbService.findLastSuccessfulTask(onboarding))
                    .verifyComplete();
    }

    @Test
    void updateTaskStatus_noRetryUpdates_succeeds() {
        when(taskStatusRepository.upsertTaskStatusEntity(any(TaskStatusEntity.class)))
                .thenReturn(Mono.just(taskStatusEntity));

        StepVerifier.create(taskDbService.updateTaskStatus(taskStatus))
                    .expectNext(taskStatus)
                    .verifyComplete();

        verify(taskStatusRepository, times(1))
                .upsertTaskStatusEntity(any(TaskStatusEntity.class));
        verifyNoInteractions(retryMessagesRepository);
    }

    @Test
    void updateTaskStatus_withRetryUpdates_succeeds() {
        taskStatus.setStatusType(TaskStatusType.RETRY);
        taskStatusEntity.setStatus(TaskStatusType.RETRY);
        final RetryMessageEntity expectedRetryMessageEntity = RetryMessageEntity.builder()
                                                                                .taskId(taskStatus.getTaskId())
                                                                                .message(taskStatus.getStatusMessage())
                                                                                .endTime(toOffsetDateTime(taskStatus.getStatusUpdateTime()).orElse(null))
                                                                                .build();

        when(taskStatusRepository.upsertTaskStatusEntity(any(TaskStatusEntity.class)))
                .thenReturn(Mono.just(taskStatusEntity));
        when(retryMessagesRepository.upsertRetryMessages(any(RetryMessageEntity.class)))
                .thenReturn(Mono.just(expectedRetryMessageEntity));

        StepVerifier.create(taskDbService.updateTaskStatus(taskStatus))
                    .expectNext(taskStatus)
                    .verifyComplete();

        verify(taskStatusRepository, times(1))
                .upsertTaskStatusEntity(any(TaskStatusEntity.class));

        // Give some time for the async saveRetryMessage call to complete
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        verify(retryMessagesRepository, times(1))
                .upsertRetryMessages(any(RetryMessageEntity.class));
    }

}
