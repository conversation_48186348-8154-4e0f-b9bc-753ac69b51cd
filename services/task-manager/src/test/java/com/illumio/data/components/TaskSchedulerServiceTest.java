package com.illumio.data.components;

import com.illumio.data.configuration.TaskManagerConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import com.illumio.data.model.mapper.TenantOnboardingMapper;
import com.illumio.data.repositories.OnboardingRepository;
import com.illumio.data.util.DateTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskSchedulerServiceTest {

    @Mock private OnboardingRepository onboardingRepository;
    @Mock private TaskDbService taskDbService;
    @Mock private TaskSenderService taskSenderService;
    @Mock private LDService ldService;
    @Mock private TaskManagerConfiguration taskManagerConfiguration;
    @Mock private TenantConfigurationBuilderService tenantConfigurationBuilderService;
    @Mock private ObjectMapper objectMapper;

    @InjectMocks
    private TaskSchedulerService taskSchedulerService;

    private final TenantOnboarding onboarding =
            TenantOnboarding.builder()
                            .tenantId("tenant1")
                            .integration(Integration.WIZ)
                            .credentialsVaultPath("/secret/data/integrations-arch/integration1/tenant1/credentials")
                            .build();

    private TenantOnboardingEntity onboardingEntity;

    @BeforeEach
    void setUp() {
        onboardingEntity = TenantOnboardingMapper.toTenantOnboardingEntity(onboarding, objectMapper);
    }

    @Test
    void scheduleTasks_shouldProcessAllOnboardings_Delta() {
        TenantOnboarding onboarding2 = TenantOnboarding.builder()
                                                       .tenantId("tenant2")
                                                       .integration(Integration.ARMIS)
                                                       .credentialsVaultPath("path2")
                                                       .build();
        TenantOnboardingEntity onboardingEntity2 =
                TenantOnboardingMapper.toTenantOnboardingEntity(onboarding2, objectMapper);


        when(onboardingRepository.findAll()).thenReturn(Flux.just(onboardingEntity, onboardingEntity2));
        when(taskDbService.findLatestTask(eq(onboarding))).thenReturn(Mono.just(SubTaskStatus.builder()
                                                                                             .statusType(TaskStatusType.SUCCESS)
                                                                                             .build()));
        when(taskDbService.findLatestTask(eq(onboarding2))).thenReturn(Mono.just(SubTaskStatus.builder()
                                                                                              .statusType(TaskStatusType.FAIL)
                                                                                              .build()));

        when(ldService.integrationIsEnabledForTenant(eq(onboarding.getTenantId()), eq(onboarding.getIntegration())))
                .thenReturn(Mono.just(true));
        when(ldService.integrationIsEnabledForTenant(eq(onboardingEntity2.getTenantId()), eq(onboardingEntity2.getIntegration())))
                .thenReturn(Mono.just(true));

        when(taskDbService.updateTaskStatus(any(SubTaskStatus.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        when(taskDbService.findLastSuccessfulTask(eq(onboarding))).thenReturn(Mono.empty());
        when(taskDbService.findLastSuccessfulTask(eq(onboarding2))).thenReturn(Mono.empty());
        when(taskSenderService.sendTask(any(SyncTask.class))).thenReturn(Mono.empty());
        when(taskDbService.determineTaskType(eq(onboarding))).thenReturn(Mono.just(TaskType.DELTA));
        when(taskDbService.determineTaskType(eq(onboarding2))).thenReturn(Mono.just(TaskType.DELTA));

        StepVerifier.create(taskSchedulerService.scheduleDeltaSyncTask())
                    .verifyComplete();

        verify(onboardingRepository).findAll();
        verify(taskDbService).findLatestTask(eq(onboarding));
        verify(taskDbService).findLatestTask(eq(onboarding2));
        verify(taskDbService).findLastSuccessfulTask(eq(onboarding));
        verify(taskDbService).findLastSuccessfulTask(eq(onboarding2));
        verify(ldService).integrationIsEnabledForTenant(eq(onboarding.getTenantId()), eq(onboarding.getIntegration()));
        verify(ldService).integrationIsEnabledForTenant(eq(onboardingEntity2.getTenantId()), eq(onboardingEntity2.getIntegration()));
        verify(taskDbService).determineTaskType(eq(onboarding));
        verify(taskDbService).determineTaskType(eq(onboarding2));

        InOrder taskDbServiceInOrder = inOrder(taskDbService);
        verifyTaskStatusUpdatedInOrder(taskDbServiceInOrder, onboarding);
        verifyTaskStatusUpdatedInOrder(taskDbServiceInOrder, onboarding2);

        InOrder taskSenderServiceInOrder = inOrder(taskSenderService);
        verifyTaskMessageSentInOrder(taskSenderServiceInOrder, onboarding);
        verifyTaskMessageSentInOrder(taskSenderServiceInOrder, onboarding2);
        // TODO: verify more about the syncTask produced; same in all other cases where verifying taskSenderService.sendTask; also verify async flow cases
    }

    private void verifyTaskStatusUpdatedInOrder(InOrder inOrder, TenantOnboarding onboarding) {
        inOrder.verify(taskDbService).updateTaskStatus(argThat(
                taskStatus -> taskStatus.getTenantId().equals(onboarding.getTenantId())
                        && taskStatus.getIntegration().equals(onboarding.getIntegration())
                        && taskStatus.getStatusType().equals(TaskStatusType.SCHEDULED)
                        && taskStatus.getTaskType().equals(TaskType.DELTA)
        ));
    }

    private void verifyTaskMessageSentInOrder(InOrder inOrder, TenantOnboarding onboarding) {
        inOrder.verify(taskSenderService, times(1)).sendTask(argThat(
                syncTask -> syncTask.getTenantId().equals(onboarding.getTenantId())
                        && syncTask.getIntegration().equals(onboarding.getIntegration())
                        && syncTask.getCredentialsVaultPath().equals(onboarding.getCredentialsVaultPath())
                        && syncTask.getTaskStatus().equals(TaskStatusType.SCHEDULED)
                        && syncTask.getTaskType().equals(TaskType.DELTA)
        ));
    }

    @Test
    void scheduleTask_shouldUseLastSuccessfulSyncTaskWhenAvailable() {
        TaskStatusEntity.TaskStatusEntityBuilder builder = TaskStatusEntity.builder()
                                                                           .taskId("task0")
                                                                           .integration(Integration.WIZ)
                                                                           .tenantId("tenant1")
                                                                           .statusMessage("Completed successfully")
                                                                           .status(TaskStatusType.SUCCESS)
                                                                           .taskType(TaskType.GREENFIELD);

        DateTimeUtil.toOffsetDateTime("2025-03-09T15:25:00Z").ifPresent(builder::scheduleTime);
        DateTimeUtil.toOffsetDateTime("2025-03-09T15:26:00Z").ifPresent(builder::startTime);
        DateTimeUtil.toOffsetDateTime("2025-03-09T15:30:00Z").ifPresent(builder::endTime);

        TaskStatusEntity lastSuccessfulTask = builder.build();

        when(onboardingRepository.findAll()).thenReturn(Flux.just(onboardingEntity));
        when(taskDbService.findLatestTask(eq(onboarding))).thenReturn(Mono.just(SubTaskStatus.builder()
                                                                                             .statusType(TaskStatusType.SUCCESS)
                                                                                             .build()));

        when(ldService.integrationIsEnabledForTenant(eq(onboarding.getTenantId()), eq(onboarding.getIntegration())))
                .thenReturn(Mono.just(true));

        when(taskDbService.updateTaskStatus(any(SubTaskStatus.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        when(taskDbService.findLastSuccessfulTask(eq(onboarding))).thenReturn(Mono.just(lastSuccessfulTask));
        when(taskSenderService.sendTask(any(SyncTask.class))).thenReturn(Mono.empty());
        when(taskDbService.determineTaskType(eq(onboarding))).thenReturn(Mono.just(TaskType.DELTA));

        StepVerifier.create(taskSchedulerService.scheduleDeltaSyncTask())
                    .verifyComplete();

        verify(ldService).integrationIsEnabledForTenant(eq(onboarding.getTenantId()), eq(onboarding.getIntegration()));

        verify(taskDbService).findLastSuccessfulTask(any());
        verify(taskSenderService).sendTask(argThat(
                syncTask -> syncTask.getTenantId().equals(onboarding.getTenantId())
                        && syncTask.getIntegration().equals(onboarding.getIntegration())
                        && syncTask.getCredentialsVaultPath().equals(onboarding.getCredentialsVaultPath())
                        && syncTask.getPriorSuccessfulSync()
                                   .equals(TaskBasicMetadata.builder()
                                                            .startTime(DateTimeUtil.toString(lastSuccessfulTask.getStartTime()).orElse(null))
                                                            .endTime(DateTimeUtil.toString(lastSuccessfulTask.getEndTime()).orElse(null))
                                                            .endMessage(lastSuccessfulTask.getStatusMessage())
                                                            .build())
        ));
    }

}