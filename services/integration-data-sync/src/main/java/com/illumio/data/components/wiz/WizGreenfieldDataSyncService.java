package com.illumio.data.components.wiz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.FindingsSenderService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WizGreenfieldDataSyncService extends WizDataSyncer {

    private final TokenService tokenService;
    private final WizReportStatusChecker wizReportStatusChecker;
    private final WizGreenfieldDataPuller wizGreenfieldDataPuller;
    private final ObjectMapper objectMapper;

    @Autowired
    public WizGreenfieldDataSyncService(final TokenService tokenService,
                                        final TaskStatusSenderService taskStatusSenderService,
                                        final WizReportStatusChecker wizReportStatusChecker,
                                        final WizGreenfieldDataPuller wizGreenfieldDataPuller,
                                        final FindingsSenderService findingsSenderService,
                                        final DataSyncFailureHandler dataSyncFailureHandler,
                                        final ObjectMapper objectMapper) {
        super(tokenService, taskStatusSenderService, dataSyncFailureHandler, objectMapper, findingsSenderService);
        this.tokenService = tokenService;
        this.wizReportStatusChecker = wizReportStatusChecker;
        this.wizGreenfieldDataPuller = wizGreenfieldDataPuller;
        this.objectMapper = objectMapper;
    }

    @Override
    public Flux<WizFinding> getIntegrationData(final SyncTask syncTask, final Token token) {
        return Flux.fromIterable(getReportIdentifiers(syncTask))
                   .flatMap(reportIdentifier -> getReportUrl(syncTask, reportIdentifier))
                   .flatMap(wizGreenfieldDataPuller::pullData);
    }

    @SneakyThrows
    private List<WizReportIdentifier> getReportIdentifiers(final SyncTask syncTask) {
        final WizReadyAsyncStatusMessage readyAsyncStatusMessage =
                objectMapper.readValue(syncTask.getStatusMessage(), WizReadyAsyncStatusMessage.class);
        return readyAsyncStatusMessage.getReportIdentifiers();
    }

    private Mono<WizReportUrl> getReportUrl(final SyncTask syncTask, final WizReportIdentifier reportIdentifier) {
        return tokenService.getAuthToken(syncTask)
                           .map(token -> WizReportStatusCheckTask.builder()
                                                                 .reportIdentifier(reportIdentifier)
                                                                 .apiUrl(syncTask.getSyncTaskConfigurations().getApiUrl())
                                                                 .authToken(token.getAccessToken())
                                                                 .integration(Integration.WIZ)
                                                                 .priorSuccessfulSync(syncTask.getPriorSuccessfulSync())
                                                                 .build())
                .flatMap(wizReportStatusChecker::checkReportStatus)
                .filter(wizReportStatus -> Objects.nonNull(wizReportStatus.getUrl()))
                .switchIfEmpty(Mono.error(new ExternalServiceException("Wiz report has expired. Greenfield data sync must be restarted.")))
                .map(wizReportStatus -> WizReportUrl.builder()
                                                    .url(wizReportStatus.getUrl())
                                                    .findingType(reportIdentifier.getFindingType())
                                                    .build());
    }

}