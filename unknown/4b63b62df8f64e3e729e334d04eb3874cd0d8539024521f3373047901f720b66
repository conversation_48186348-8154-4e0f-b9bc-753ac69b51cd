package com.illumio.data.components.mapper;

import com.illumio.data.entities.IssueEntity;
import com.illumio.data.model.Issue;
import lombok.experimental.UtilityClass;

@UtilityClass
public class IssueEntityMapper {
    public static IssueEntity toIssueEntity(Issue issue) {
        return IssueEntity.builder()
                          .id(issue.getId())
                          .sourceRuleNames(issue.getMetadata().getSourceRuleNames())
                          .severity(issue.getMetadata().getSeverity())
                          .subscriptionId(issue.getResource().getSubscriptionId())
                          .status(issue.getStatus())
                          .risks(issue.getMetadata().getRisks().toArray(new String[0]))
                          .tenantId(issue.getTenantId())
                          .integration(issue.getIntegration())
                          .workloadId(issue.getResource().getCloudProviderUniqueId())
                          .build();
    }
}
