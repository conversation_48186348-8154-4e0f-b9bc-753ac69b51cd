package com.illumio.data.components;

import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.Integration;
import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.server.integrations.reactor.LDReactorClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


import static com.illumio.data.util.LaunchDarklyCommonConstants.LD_FLAG_ATTRIBUTE_TENANT;

@Slf4j
@Service
@RequiredArgsConstructor
public class LDService {

    private final String ldReactorClientUUID;
    private final LDReactorClient ldReactorClient;

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Boolean> integrationIsEnabledForTenant(final String tenantId, final Integration integration) {
        final LDContext ldContext = LDContext.builder(ldReactorClientUUID)
                                             .anonymous(true)
                                             .set(LD_FLAG_ATTRIBUTE_TENANT, tenantId)
                                             .build();

        final String featureFlagName = getFlagNameForIntegration(integration);
        return ldReactorClient.boolVariation(featureFlagName, ldContext, false)
                              .doOnNext(flagValue -> log.debug("Using value={} for featureFlag={} on tenantId={}, integration={}",
                                      flagValue, featureFlagName, tenantId, integration));
    }

    public String getFlagNameForIntegration(final Integration integration) {
        return String.format("integration-%s-enabled", integration.name().toLowerCase());
    }

}
